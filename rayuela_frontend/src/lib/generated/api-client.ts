import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from "axios";
// import { toast } from 'sonner'; // Comentado porque no se usa actualmente

// Interfaces de error
export interface ApiErrorDetails {
  field?: string;
  message: string;
  code?: string;
}

export interface ApiErrorResponse {
  message: string;
  error_code: string;
  details?: ApiErrorDetails[];
  status_code: number;
}

export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public errorCode: string,
    public details?: ApiErrorDetails[] | null,
  ) {
    super(message);
    this.name = "ApiError";
  }

  static isApiError(error: unknown): error is ApiError {
    return error instanceof ApiError;
  }

  static fromResponse(response: ApiErrorResponse): ApiError {
    return new ApiError(
      response.message,
      response.status_code,
      response.error_code,
      response.details,
    );
  }
}

// Obtén la URL base de las variables de entorno
const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_URL || "http://localhost:8001/api/v1";

// Cliente Axios personalizado
export const customInstance = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// CustomRequestConfig no longer necesario; mantener por compatibilidad mínima
export interface CustomRequestConfig extends AxiosRequestConfig {}

// Interceptor de solicitud para añadir cabeceras de autenticación
customInstance.interceptors.request.use((config) => {
  // Detect execution in browser
  if (typeof window !== "undefined") {
    const token = localStorage.getItem("rayuela-token");
    const apiKey = localStorage.getItem("rayuela-apiKey");

    config.headers = config.headers ?? {};

    if (token) {
      config.headers["Authorization"] = `Bearer ${token}`;
    }

    if (apiKey && !config.url?.includes("/auth/token") && !config.url?.includes("/auth/register")) {
      config.headers["X-API-Key"] = apiKey;
    }
  }

  return config;
});

// Interceptor de respuesta para manejar errores
customInstance.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error: AxiosError<ApiErrorResponse>) => {
    if (error.response) {
      const errorResponse = error.response.data;
      throw ApiError.fromResponse(errorResponse);
    } else if (error.request) {
      // La solicitud se realizó pero no se recibió respuesta
      throw new ApiError(
        "No se recibió respuesta del servidor",
        0,
        "NETWORK_ERROR",
        null,
      );
    } else {
      // Error al configurar la solicitud
      throw new ApiError(error.message, 0, "REQUEST_ERROR", null);
    }
  },
);

export default customInstance;
