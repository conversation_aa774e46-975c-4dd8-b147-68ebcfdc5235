from sqlalchemy import (
    Column,
    Integer,
    String,
    DateTime,
    ForeignKey,
    Text,
    UniqueConstraint,
    Index,
    func,
    JSON,
    PrimaryKeyConstraint,
    Identity,
)
from sqlalchemy.orm import relationship
from src.db.base import Base
from .mixins import TenantMixin, ACCOUNT_ID_FK, get_tenant_table_args


class AuditLog(Base, TenantMixin):
    __tablename__ = "audit_logs"

    audit_id = Column(Integer, Identity(), primary_key=True)  # Match database schema
    account_id = Column(Integer, ForeignKey(ACCOUNT_ID_FK), nullable=False)
    user_id = Column(Integer, nullable=True)  # Match database schema
    action = Column(String(100), nullable=False)  # Match database schema
    entity_type = Column(String(100), nullable=False)  # Match database schema
    entity_id = Column(Integer, nullable=True)  # Match database schema
    old_values = Column(JSON, nullable=True)  # Match database schema
    new_values = Column(JSON, nullable=True)  # Match database schema
    timestamp = Column(DateTime(timezone=True), server_default=func.now())  # Match database schema
    ip_address = Column(String(45), nullable=True)  # Match database schema
    user_agent = Column(Text, nullable=True)  # Match database schema

    __table_args__ = get_tenant_table_args(
        # Simple FK to match database schema
        ForeignKeyConstraint(
            ["user_id"],
            ["system_users.user_id"],
            ondelete="SET NULL",
            name="audit_logs_user_id_fkey"
        ),
        Index("idx_audit_account_timestamp", "account_id", "timestamp")
    )

    account = relationship("Account", back_populates="audit_log")
