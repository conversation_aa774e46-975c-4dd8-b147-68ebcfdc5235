from sqlalchemy import (
    Column,
    Integer,
    Identity,
    DateTime,
    ForeignKey,
    Float,
    DECIMAL,
    UniqueConstraint,
    Index,
    func,
    Enum as SQLAEnum,
    ForeignKeyConstraint,
    PrimaryKeyConstraint,
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from src.db.base import Base
from src.db.enums import InteractionType
from .mixins import TenantMixin, ACCOUNT_ID_FK, get_tenant_table_args


class Interaction(Base, TenantMixin):
    __tablename__ = "interactions"

    account_id = Column(Integer, ForeignKey(ACCOUNT_ID_FK), primary_key=True)
    id = Column(Integer, Identity(), primary_key=True)
    user_id = Column(Integer, nullable=False)
    product_id = Column(Integer, nullable=False)
    interaction_type = Column(SQLAEnum(InteractionType), nullable=False)
    value = Column(DECIMAL(5, 2), nullable=True)  # Changed from Float to DECIMAL for precise rating calculations
    timestamp = Column(DateTime(timezone=True), server_default=func.now())
    recommendation_metadata = Column(JSONB, nullable=True)  # Para almacenar información adicional como confianza, tipo de modelo, etc.

    __table_args__ = get_tenant_table_args(
        PrimaryKeyConstraint("account_id", "id"),
        # Composite FKs for proper tenant isolation
        ForeignKeyConstraint(
            ["account_id", "user_id"],
            ["end_users.account_id", "end_users.user_id"],
            ondelete="CASCADE",
            name="fk_interaction_user"
        ),
        ForeignKeyConstraint(
            ["account_id", "product_id"],
            ["products.account_id", "products.product_id"],
            ondelete="CASCADE",
            name="fk_interaction_product"
        ),
        Index("idx_interaction_account_user", "account_id", "user_id"),
        Index("idx_interaction_account_product", "account_id", "product_id"),
        Index("idx_interaction_account_timestamp", "account_id", "timestamp"),
    )

    # Relationships
    account = relationship("Account", back_populates="interactions")
    end_user = relationship(
        "EndUser",
        foreign_keys=[account_id, user_id],
        primaryjoin="and_(Interaction.account_id==EndUser.account_id, Interaction.user_id==EndUser.user_id)",
        back_populates="interactions"
    )
    product = relationship(
        "Product",
        foreign_keys=[account_id, product_id],
        primaryjoin="and_(Interaction.account_id==Product.account_id, Interaction.product_id==Product.product_id)",
        back_populates="interactions"
    )
