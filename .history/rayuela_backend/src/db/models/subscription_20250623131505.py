from sqlalchemy import (
    Column,
    Integer,
    String,
    DateTime,
    Foreign<PERSON>ey,
    func,
    Boolean,
    BigInteger,
    Enum as SQLAEnum,
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from src.db.base import Base
from .mixins import TenantMixin, ACCOUNT_ID_FK, get_tenant_table_args
from src.db.enums import SubscriptionPlan


class Subscription(Base):
    __tablename__ = "subscriptions"

    # La clave primaria es solo account_id, lo que refuerza la relación 1:1 con Account
    account_id = Column(Integer, ForeignKey(ACCOUNT_ID_FK), primary_key=True)

    # Campos de la suscripción
    plan_type = Column(SQLAEnum(SubscriptionPlan), nullable=False, index=True)
    api_calls_limit = Column(Integer)
    storage_limit = Column(Integer)
    
    # Estado de la suscripción
    is_active = Column(Boolean, default=True)
    expires_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime(timezone=True), default=func.now(), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), default=func.now(), server_default=func.now(), onupdate=func.now())

    # Campos de seguimiento de uso
    api_calls_used_current_period = Column(Integer, default=0)
    storage_used_current_period = Column(BigInteger, default=0)
    period_start = Column(DateTime(timezone=True), nullable=True)
    period_end = Column(DateTime(timezone=True), nullable=True)

    # Campos de Mercado Pago
    mercadopago_subscription_id = Column(String(255), index=True, nullable=True)
    mercadopago_price_id = Column(String(255), nullable=True)

    # Campo para indicar qué pasarela de pago se está utilizando
    payment_gateway = Column(String(20), nullable=True, default="mercadopago")

    # Campos de seguimiento de uso adicionales
    monthly_api_calls_used = Column(Integer, nullable=False, default=0)
    storage_used = Column(BigInteger, nullable=False, default=0)  # en bytes
    last_reset_date = Column(DateTime(timezone=True), nullable=True)
    available_models = Column(JSONB, nullable=True)  # Lista de modelos disponibles

    # Campo adicional para features
    additional_features = Column(JSONB, default=dict)

    # Campo para seguimiento de frecuencia de entrenamiento
    last_successful_training_at = Column(DateTime(timezone=True), nullable=True, comment="Fecha del último entrenamiento exitoso")

    __table_args__ = get_tenant_table_args()

    account = relationship("Account", back_populates="subscription")
