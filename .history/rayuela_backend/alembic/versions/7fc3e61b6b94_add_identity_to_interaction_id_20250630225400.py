"""add_identity_to_interaction_id

Revision ID: 7fc3e61b6b94
Revises: 1298e9a0afec
Create Date: 2025-06-30 22:54:01.265022

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '7fc3e61b6b94'
down_revision: Union[str, None] = '1298e9a0afec'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
