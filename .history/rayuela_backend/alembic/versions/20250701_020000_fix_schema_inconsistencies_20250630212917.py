"""Fix schema inconsistencies between models and database

Revision ID: 20250701_020000
Revises: 20250701_010001
Create Date: 2025-07-01 02:00:00.000000

This migration fixes critical inconsistencies between SQLAlchemy models
and the actual database schema created by the initial migration.

Key fixes:
1. EndUser: Change primary key from end_user_id to (user_id, account_id)
2. Interaction: Change primary key from interaction_id to (id, account_id) 
3. Search: Change primary key from search_id to (id, account_id)
4. AuditLog: Change primary key from audit_id to (id, account_id)
5. Update foreign key references accordingly
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from typing import Sequence, Union

# revision identifiers, used by Alembic.
revision: str = "20250701_020000"
down_revision: Union[str, None] = "20250701_010001"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Apply schema consistency fixes."""
    
    # 1. Fix EndUser table structure
    print("Fixing EndUser table structure...")
    
    # Drop existing constraints and indexes that will be affected
    op.drop_constraint('uq_end_user_account_external_id', 'end_users', type_='unique')
    op.drop_constraint('end_users_pkey', 'end_users', type_='primary')
    
    # Rename end_user_id to user_id and make it part of composite primary key
    op.alter_column('end_users', 'end_user_id', new_column_name='user_id')
    
    # Add composite primary key (user_id, account_id)
    op.create_primary_key('end_users_pkey', 'end_users', ['user_id', 'account_id'])
    
    # Recreate unique constraint with correct name
    op.create_unique_constraint('uq_end_user_external_id', 'end_users', ['account_id', 'external_id'])
    
    
    # 2. Fix Interaction table structure
    print("Fixing Interaction table structure...")
    
    # Drop foreign key constraints first
    op.drop_constraint('interactions_end_user_id_fkey', 'interactions', type_='foreignkey')
    op.drop_constraint('interactions_product_id_fkey', 'interactions', type_='foreignkey')
    op.drop_constraint('interactions_pkey', 'interactions', type_='primary')
    
    # Rename columns to match model expectations
    op.alter_column('interactions', 'interaction_id', new_column_name='id')
    op.alter_column('interactions', 'end_user_id', new_column_name='user_id')
    op.alter_column('interactions', 'rating', new_column_name='value')
    op.alter_column('interactions', 'context', new_column_name='recommendation_metadata')
    
    # Remove columns that don't exist in the model
    op.drop_column('interactions', 'session_id')
    op.drop_column('interactions', 'created_at')
    op.drop_column('interactions', 'updated_at')
    
    # Add composite primary key (account_id, id)
    op.create_primary_key('interactions_pkey', 'interactions', ['account_id', 'id'])
    
    # Recreate foreign key constraints with composite keys
    op.create_foreign_key(
        'fk_interaction_user', 'interactions', 'end_users',
        ['account_id', 'user_id'], ['account_id', 'user_id'],
        ondelete='CASCADE'
    )
    op.create_foreign_key(
        'fk_interaction_product', 'interactions', 'products', 
        ['account_id', 'product_id'], ['account_id', 'product_id'],
        ondelete='CASCADE'
    )
    
    
    # 3. Fix Search table structure  
    print("Fixing Search table structure...")
    
    # Drop constraints
    op.drop_constraint('searches_end_user_id_fkey', 'searches', type_='foreignkey')
    op.drop_constraint('searches_pkey', 'searches', type_='primary')
    
    # Rename columns
    op.alter_column('searches', 'search_id', new_column_name='id')
    op.alter_column('searches', 'end_user_id', new_column_name='user_id')
    
    # Remove columns that don't exist in the model
    op.drop_column('searches', 'filters')
    op.drop_column('searches', 'results_count')
    op.drop_column('searches', 'session_id')
    op.drop_column('searches', 'created_at')
    op.drop_column('searches', 'updated_at')
    
    # Add composite primary key (account_id, id)
    op.create_primary_key('searches_pkey', 'searches', ['account_id', 'id'])
    
    # Recreate foreign key constraint
    op.create_foreign_key(
        'fk_search_user', 'searches', 'end_users',
        ['account_id', 'user_id'], ['account_id', 'user_id'],
        ondelete='CASCADE'
    )


    # 4. Fix AuditLog table structure
    print("Fixing AuditLog table structure...")

    # Drop constraints
    op.drop_constraint('audit_logs_user_id_fkey', 'audit_logs', type_='foreignkey')
    op.drop_constraint('audit_logs_pkey', 'audit_logs', type_='primary')

    # Rename columns to match model expectations
    op.alter_column('audit_logs', 'audit_id', new_column_name='id')
    op.alter_column('audit_logs', 'old_values', new_column_name='changes')
    op.alter_column('audit_logs', 'timestamp', new_column_name='created_at')

    # Remove columns that don't exist in the model
    op.drop_column('audit_logs', 'user_id')
    op.drop_column('audit_logs', 'new_values')
    op.drop_column('audit_logs', 'ip_address')
    op.drop_column('audit_logs', 'user_agent')

    # Add columns that exist in the model but not in migration
    op.add_column('audit_logs', sa.Column('performed_by', sa.String(), nullable=False, server_default='system'))
    op.add_column('audit_logs', sa.Column('details', sa.Text(), nullable=True))

    # Add composite primary key (account_id, id)
    op.create_primary_key('audit_logs_pkey', 'audit_logs', ['account_id', 'id'])


def downgrade() -> None:
    """Revert schema consistency fixes."""
    
    # This is a complex downgrade that would need to reverse all the changes
    # For now, we'll raise an exception to prevent accidental downgrades
    raise NotImplementedError(
        "Downgrade not implemented for schema consistency fixes. "
        "This migration makes structural changes that are difficult to reverse safely."
    )
