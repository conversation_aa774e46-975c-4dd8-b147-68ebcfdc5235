import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { generateMetadata as generateSEOMetadata } from '@/lib/seo';
import { Check, Star } from "lucide-react";
import { Container, Stack, IconText } from '@/components/ui/spacing-system';

export const metadata = generateSEOMetadata({
  title: 'Inversión Estratégica en Ventaja Competitiva - Planes Rayuela',
  description: 'La decisión que separa líderes de seguidores. Planes diseñados para CTOs que entienden que la personalización IA no es un gasto, es poder de mercado.',
  path: '/pricing',
  keywords: ['inversión estratégica', 'ventaja competitiva', 'personalización enterprise', 'liderazgo tecnológico', 'poder de mercado'],
});

const plans = [
  {
    id: "sandbox",
    name: "Developer Sandbox",
    price: "Grat<PERSON>",
    period: "para siempre",
    description: "Prueba el poder sin riesgo - La decisión inteligente antes de la inversión",
    features: [
      "1,000 Llamadas API de Recomendación/mes",
      "1 modelo de recomendación",
      "API básica",
      "Documentación completa",
      "Soporte por email",
      "🧪 Ideal para validar el impacto",
      "🔄 Reset de datos incluido"
    ],
    cta: "Validar Impacto Gratis",
    href: "/register",
    popular: false
  },
  {
    id: "starter",
    name: "Starter",
    price: "ARS $49.000",
    period: "/mes",
    description: "La ventaja que necesitas para destacar en tu mercado",
    features: [
      "100,000 Llamadas API de Recomendación/mes",
      "Hasta 3 modelos",
      "Almacenamiento 10 GB",
      "Entrenamiento mensual",
      "Webhooks y callbacks",
      "Soporte estándar",
      "SLA 99.9%"
    ],
    cta: "Obtener Ventaja",
    href: "/register",
    popular: false
  },
  {
    id: "pro",
    name: "Pro",
    price: "ARS $199.000",
    period: "/mes",
    description: "El poder que usan los líderes para dominar su competencia",
    features: [
      "500,000 Llamadas API de Recomendación/mes",
      "Hasta 10 modelos",
      "Almacenamiento 50 GB",
      "Entrenamiento semanal",
      "API avanzada con analytics",
      "Soporte prioritario",
      "SLA 99.9%"
    ],
    cta: "Dominar Competencia",
    href: "/register",
    popular: true
  },
  {
    id: "enterprise",
    name: "Enterprise",
    price: "ARS personalizado",
    period: "",
    description: "Poder absoluto para organizaciones que definen el mercado",
    features: [
      "Llamadas API de Recomendación ilimitadas",
      "Modelos personalizados y A/B testing",
      "Infraestructura dedicada",
      "Integración personalizada",
      "Soporte 24/7",
      "SLA 99.99%",
      "Consultoría incluida"
    ],
    cta: "Definir el Mercado",
    href: "/contact-sales",
    popular: false
  }
];

export default function PricingPage() {
  const offerSchema = {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: 'Rayuela Recommendation API',
    description: 'Sistema de recomendaciones como servicio',
    offers: plans.map(plan => ({
      '@type': 'Offer',
      name: plan.name,
      description: plan.description,
      price: plan.price === 'Gratis' ? '0' : plan.price.replace('$', ''),
      priceCurrency: 'ARS',
      availability: 'https://schema.org/InStock',
    })),
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(offerSchema),
        }}
      />

      <div className="min-h-screen bg-gradient-to-br from-marketing-gradient-start to-marketing-gradient-end">
        <Container spacing="loose" maxWidth="full">
          <Stack spacing="xl">
            {/* Hero Section */}
            <div className="text-center">
              <Stack spacing="lg">
                <h1 className="text-heading-large md:text-display text-gray-900 dark:text-white">
                  La Inversión que Separa Líderes de Seguidores
                </h1>
                <p className="text-body-large text-gray-600 dark:text-gray-300 max-w-4xl mx-auto">
                  Los CTOs más exitosos de LATAM no ven esto como un gasto—lo ven como poder de mercado. Mientras tus competidores debaten costos, tú ya estarás dominando con personalización que convierte.
                </p>
                <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-700 rounded-lg p-4 max-w-md mx-auto">
                  <p className="text-sm text-amber-800 dark:text-amber-200 font-medium">
                    ⚡ Solo 23 espacios disponibles para early adopters
                  </p>
                </div>
              </Stack>
            </div>

            {/* Pricing Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {plans.map((plan) => (
              <Card key={plan.id} className={`relative h-full ${plan.popular ? 'border-blue-500 shadow-lg scale-105' : ''}`}>
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-blue-500 text-white px-4 py-1">
                      <IconText icon={<Star className="w-4 h-4" />} size="sm">
                        Más Popular
                      </IconText>
                    </Badge>
                  </div>
                )}

                <CardHeader className="text-center">
                  <CardTitle className="text-heading">{plan.name}</CardTitle>
                  <div className="mt-4">
                    <span className="text-metric-large text-gray-900 dark:text-white">
                      {plan.price}
                    </span>
                    <span className="text-gray-600 dark:text-gray-300">
                      {plan.period}
                    </span>
                  </div>
                  <CardDescription className="mt-2">
                    {plan.description}
                  </CardDescription>
                </CardHeader>

                <CardContent className="flex-1">
                  <Stack spacing="md" className="mb-8">
                    {plan.features.map((feature, featureIndex) => (
                      <IconText 
                        key={featureIndex} 
                        icon={<Check className="w-5 h-5 text-green-500 flex-shrink-0" />}
                        align="start"
                      >
                        <span className="text-gray-700 dark:text-gray-300">{feature}</span>
                      </IconText>
                    ))}
                  </Stack>

                  <Button
                    asChild
                    className="w-full"
                    variant={plan.popular ? "default" : "outline"}
                    size="lg"
                  >
                    <Link href={plan.href}>{plan.cta}</Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* FAQ Section */}
          <div className="bg-white dark:bg-gray-800 rounded-lg p-8 shadow-lg">
            <h2 className="text-heading-large text-center text-gray-900 dark:text-white mb-8">
              Preguntas Frecuentes
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-subheading text-gray-900 dark:text-white mb-2">
                  ¿Qué sucede si excedo mi límite de llamadas API?
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Recibirás notificaciones cuando alcances el 80 % y el 100 % de tu cuota. El servicio se bloqueará temporalmente al exceder el límite para evitar cargos inesperados. Puedes actualizar tu plan al instante para restablecer el servicio.
                </p>
              </div>

              <div>
                <h3 className="text-subheading text-gray-900 dark:text-white mb-2">
                  ¿Puedo cambiar de plan?
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Sí. Los upgrades se prorratean y se aplican de inmediato. Los downgrades se aplican al finalizar tu ciclo de facturación actual.
                </p>
              </div>

              <div>
                <h3 className="text-subheading text-gray-900 dark:text-white mb-2">
                  ¿Hay período de prueba para los planes pagos?
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  El plan <strong>Developer Sandbox</strong> es gratis para siempre. Además, los planes <strong>Starter</strong> y <strong>Pro</strong> incluyen 14 días de prueba gratuita sin necesidad de tarjeta.
                </p>
              </div>

              <div>
                <h3 className="text-subheading text-gray-900 dark:text-white mb-2">
                  Métodos de pago
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Aceptamos Mercado Pago y tarjetas de crédito/débito (Visa, Mastercard, American Express). Los datos se procesan con cifrado de nivel bancario.
                </p>
              </div>
            </div>
          </div>
          </Stack>
        </Container>
      </div>
    </>
  );
}
