import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { generateMetadata as generateSEOMetadata, generateJsonLd } from '@/lib/seo';
import { Container, Stack, Group } from '@/components/ui/spacing-system';
import { 
  Zap, 
  Shield, 
  BarChart3, 
  Code, 
  Globe, 
  Users,
  Brain,
  Rocket,
  Lock
} from "lucide-react";
import { SemanticIcon } from '@/components/ui/icon';

export const metadata = generateSEOMetadata({
  title: 'Características Avanzadas de Personalización E-commerce - Rayuela',
  description: 'Funcionalidades avanzadas para impulsar la conversión y el engagement de tu negocio digital: recomendaciones IA, integración de pagos, explicabilidad y más.',
  path: '/features',
  keywords: ['personalización e-commerce', 'recomendaciones IA', 'explicabilidad ML', 'API e-commerce', 'machine learning'],
});

const features = [
  {
    icon: Zap,
    title: "Aumenta tus Ventas Inmediatamente",
    description: "Recomendaciones personalizadas que impulsan la conversión y el valor promedio del carrito en tu e-commerce."
  },
  {
    icon: Brain,
    title: "IA de Nivel Enterprise al Alcance de tu Negocio",
    description: "Algoritmos híbridos y Learning-to-Rank que antes solo tenían las grandes empresas, ahora accesibles para tu negocio."
  },
  {
    icon: Code,
    title: "Integración en Minutos, no Meses",
    description: "API-first diseñada por desarrolladores para desarrolladores. SDKs listos para Python, JavaScript y PHP."
  },
  {
    icon: Globe,
    title: "Diseñado para Mercados Globales",
    description: "Integración nativa con Mercado Pago y precios adaptados al mercado local. Soporte en español."
  },
  {
    icon: Shield,
    title: "Explicaciones Claras de cada Recomendación",
    description: "Entiende por qué se recomienda cada producto. Sin 'cajas negras' - toma decisiones informadas sobre tu estrategia."
  },
  {
    icon: BarChart3,
    title: "Métricas que Importan para tu Negocio",
    description: "Dashboards claros con métricas de conversión, engagement y ROI. Ve el impacto real en tus ventas."
  },
  {
    icon: Users,
    title: "Crece sin Preocupaciones Técnicas",
    description: "Arquitectura multi-tenant que escala automáticamente desde 100 hasta millones de usuarios."
  },
  {
    icon: Rocket,
    title: "Resultados desde el Primer Día",
    description: "Implementación completa en horas. Empieza a ver mejoras en conversión desde las primeras recomendaciones."
  },
  {
    icon: Lock,
    title: "Tus Datos, tu Control",
    description: "Cumplimiento GDPR, privacidad por diseño y control total sobre tus datos. Sin vendor lock-in."
  }
];

export default function FeaturesPage() {
  const softwareSchema = generateJsonLd('SoftwareApplication', {
    name: 'Rayuela - Personalización E-commerce IA',
    description: 'Plataforma global de recomendaciones inteligentes para negocios de E-commerce y contenido digital',
    featureList: features.map(f => f.title),
  });

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(softwareSchema),
        }}
      />
      
      <div className="min-h-screen bg-gradient-to-br from-marketing-gradient-start to-marketing-gradient-end">
        <Container spacing="loose" maxWidth="full">
          <Stack spacing="xl">
            {/* Hero Section */}
            <div className="text-center">
              <Stack spacing="lg">
                <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white">
                  El Arsenal que Usan los Líderes para Dominar
                </h1>
                <p className="text-xl text-gray-600 dark:text-gray-300 max-w-4xl mx-auto">
                  Mientras tus competidores luchan con soluciones básicas, tú tendrás acceso al mismo poder tecnológico que usan Amazon y Netflix. La diferencia no es solo técnica—es estratégica.
                </p>
              </Stack>
            </div>

            {/* Features Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {features.map((feature, index) => {
                const Icon = feature.icon;
                return (
                  <Card key={index} className="h-full">
                    <CardHeader>
                      <Stack spacing="md">
                        <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                          <SemanticIcon icon={Icon} size="lg" context="primary" />
                        </div>
                        <CardTitle className="text-xl">{feature.title}</CardTitle>
                      </Stack>
                    </CardHeader>
                    <CardContent>
                      <CardDescription className="text-base">
                        {feature.description}
                      </CardDescription>
                    </CardContent>
                  </Card>
                );
              })}
            </div>

            {/* CTA Section */}
            <div className="text-center bg-white dark:bg-gray-800 rounded-lg p-8 shadow-lg">
              <Stack spacing="lg">
                <div>
                  <Stack spacing="md">
                    <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
                      ¿Listo para Transformar tu E-commerce?
                    </h2>
                    <p className="text-lg text-gray-600 dark:text-gray-300">
                      Únete a las empresas que ya están impulsando sus ventas con personalización inteligente
                    </p>
                  </Stack>
                </div>
                <Group spacing="normal" wrap className="justify-center">
                  <Button asChild size="lg">
                    <Link href="/register">Regístrate Gratis y Prueba Rayuela</Link>
                  </Button>
                  <Button variant="outline" size="lg" asChild>
                    <Link href="/contact-sales">Solicita Demo Personalizada</Link>
                  </Button>
                </Group>
              </Stack>
            </div>
          </Stack>
        </Container>
      </div>
    </>
  );
}
